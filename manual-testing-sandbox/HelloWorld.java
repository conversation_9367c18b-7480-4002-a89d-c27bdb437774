import java.netAddress;
import java.net.UnknownHostException;

public class HelloWorld {

    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }

    public static String getCurrentTime() {
        String currentTime = String.valueOf(System.currentTimeMillis());
        System.out.println(currentTime);
        for (int i = 0; i < 5; i++) {
            System.out.println("Hello, World!");
        }
        return currentTime;
    }

    public static String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            String ipAddress = localHost.getHostAddress();
            return "Local IP address: " + ipAddress;
        } catch (UnknownHostException e) {
            return "Unable to determine the IP address of the local host";
        }
    }
}
