// 导入网络相关类，用于获取本地IP地址
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * HelloWorld类
 * 这是一个Java示例程序，用于演示基本的Java语法和功能
 * 包含控制台输出、字符串处理、时间获取和网络功能
 */
public class HelloWorld {

    /**
     * 程序入口点
     * 输出"Hello, World!"并展示空值检查
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 向控制台输出经典的"Hello, World!"信息

        

    }

    /**
     * 获取当前系统时间
     * 同时演示一些基本的输出语句和循环结构
     *
     * @return 当前时间的字符串表示
     */
    public static String getCurrentTime() {
        // 获取当前系统时间
        String currentTime = String.valueOf(System.currentTimeMillis());
        // 输出当前时间
        System.out.println(currentTime);
        // 使用循环打印"Hello, World!"信息

        for (int i = 0; i < 5; i++) {
            System.out.println("Hello, World!");
        }
        // 返回当前时间的字符串表示
        return currentTime;
    }

    /**
     * 获取本地主机的IP地址
     * 演示网络相关API的使用和异常处理
     *
     * @return 包含本地IP地址信息的字符串
     */
    public static String getLocalIpAddress() {
        try {
            // 获取本地主机的IP地址
            InetAddress localHost = InetAddress.getLocalHost();
            // 获取IP地址字符串
            String ipAddress = localHost.getHostAddress();
            // 返回包含本地IP地址信息的字符串
            return "本地IP地址为：" + ipAddress;
        } catch (UnknownHostException e) {
            // 异常处理：当无法确定主机IP地址时返回错误信息
            return "Unable to determine the IP address of the local host";
        }
    }
}
